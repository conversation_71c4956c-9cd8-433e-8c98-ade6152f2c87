import os
import sys
import json
import logging
import argparse
import traceback
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
import time

try:
    import hl7
    import google.generativeai as genai
    import requests
    from dotenv import load_dotenv
except ImportError as e:
    print(f"❌ Missing required dependency: {e}")
    print("Please install dependencies: pip install -r requirements.txt")
    sys.exit(1)

# Load environment variables
load_dotenv()

class HL7ProcessingError(Exception):
    """Custom exception for HL7 processing errors"""
    def __init__(self, message: str, error_code: str, file_path: str = None):
        self.message = message
        self.error_code = error_code
        self.file_path = file_path
        super().__init__(self.message)

class HL7Processor:
    """Main HL7 processing class with AI-powered error recovery"""
    
    def __init__(self, source_dir: str, output_dir: str, verbose: bool = False):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.quarantine_dir = Path("quarantine")
        self.verbose = verbose
        
        # Statistics
        self.stats = {
            'files_processed': 0,
            'files_enhanced': 0,
            'errors_encountered': 0,
            'files_quarantined': 0
        }
        
        # Setup logging
        self.setup_logging()
        
        # Load configuration
        self.load_config()
        
        # Initialize AI client
        self.init_ai_client()
        
        # Create directories
        self.create_directories()
    
    def setup_logging(self):
        """Setup dual logging system"""
        # Activity log (human-readable)
        activity_handler = logging.FileHandler('processing_activity.log')
        activity_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        activity_handler.setFormatter(activity_formatter)
        
        # Setup main logger
        self.logger = logging.getLogger('hl7_processor')
        self.logger.setLevel(logging.DEBUG if self.verbose else logging.INFO)
        self.logger.addHandler(activity_handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter('%(message)s')
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # Error log (machine-readable JSON)
        self.error_log_path = Path('error_details.log')
    
    def log_error(self, error: HL7ProcessingError, context: Dict[str, Any] = None):
        """Log error in machine-readable JSON format"""
        error_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': 'error',
            'message': error.message,
            'filePath': str(error.file_path) if error.file_path else None,
            'errorCode': error.error_code,
            'errorContext': context or {},
            'service': 'hl7-enhancer'
        }
        
        with open(self.error_log_path, 'a', encoding='utf-8') as f:
            f.write(json.dumps(error_entry) + '\n')
    
    def load_config(self):
        """Load mapping rules and configuration"""
        try:
            with open('mapping_rules.json', 'r') as f:
                self.mapping_rules = json.load(f)
            self.logger.info(f"Loaded {len(self.mapping_rules)} mapping rules")
        except FileNotFoundError:
            self.logger.warning("mapping_rules.json not found, using empty rules")
            self.mapping_rules = []
        except json.JSONDecodeError as e:
            raise HL7ProcessingError(
                f"Invalid JSON in mapping_rules.json: {e}",
                "CONFIG_ERROR"
            )
    
    def init_ai_client(self):
        """Initialize Google Gemini AI client"""
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            self.logger.warning("GEMINI_API_KEY not found, AI recovery disabled")
            self.ai_enabled = False
            return
        
        try:
            genai.configure(api_key=api_key)
            self.ai_model = genai.GenerativeModel('gemini-2.5-pro-preview-05-06')
            self.ai_enabled = True
            self.logger.info("AI-powered error recovery enabled")
        except Exception as e:
            self.logger.warning(f"Failed to initialize AI client: {e}")
            self.ai_enabled = False
    
    def create_directories(self):
        """Create necessary directories and clear output directory if it exists"""
        # Clear output directory if it exists and has content
        if self.output_dir.exists():
            import shutil
            try:
                shutil.rmtree(self.output_dir)
                self.logger.info(f"Cleared existing output directory: {self.output_dir}")
            except Exception as e:
                self.logger.warning(f"Failed to clear output directory: {e}")
        
        for directory in [self.output_dir, self.quarantine_dir]:
            try:
                directory.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                raise HL7ProcessingError(
                    f"Failed to create directory {directory}: {e}",
                    "DIRECTORY_ERROR"
                )
    
    def find_hl7_files(self) -> List[Path]:
        """Recursively find all HL7 files"""
        hl7_files = []
        try:
            for file_path in self.source_dir.rglob('*.hl7'):
                if file_path.is_file():
                    hl7_files.append(file_path)
            return hl7_files
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to scan directory {self.source_dir}: {e}",
                "DIRECTORY_ERROR"
            )
    
    def parse_hl7_message(self, file_path: Path) -> hl7.Message:
        """Parse HL7 file into message object"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()
            
            if not content:
                raise HL7ProcessingError(
                    "Empty file",
                    "HL7_PARSING_FAILURE",
                    str(file_path)
                )
            
            # Preprocess HL7 content: ensure proper segment separators
            # HL7 library expects \r as segment separator, not \n
            import re
            if '\n' in content and '\r' not in content:
                # Convert newlines to carriage returns for HL7 library
                content = content.replace('\n', '\r')
            elif '\n' not in content and '\r' not in content:
                # Split by segment identifiers and rejoin with carriage returns
                content = re.sub(r'(?=MSH|EVN|PID|PV1|OBX|OBR|NTE|AL1|DG1|PR1|GT1|IN1|IN2|IN3|ACC|UB1|UB2|ZQA|ZFM)', r'\r', content)
                content = content.strip()
            
            # Parse HL7 message (hl7 library expects \r as segment separator)
            message = hl7.parse(content)
            
            # Validate MSH segment exists
            if not message.segment('MSH'):
                raise HL7ProcessingError(
                    "MSH segment not found",
                    "HL7_PARSING_FAILURE",
                    str(file_path)
                )
            
            return message
            
        except hl7.ParseException as e:
            raise HL7ProcessingError(
                f"HL7 parsing failed: {e}",
                "HL7_PARSING_FAILURE",
                str(file_path)
            )
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to read file: {e}",
                "FILE_READ_ERROR",
                str(file_path)
            )
    
    def apply_hl7_enhancements(self, message: hl7.Message) -> hl7.Message:
        """Apply standard HL7 enhancements"""
        try:
            # Get MSH segment
            msh = message.segment('MSH')
            
            # Update version to 2.8
            msh[12] = '2.8'
            
            # Set processing ID to Production
            msh[11] = 'P'
            
            # Apply OBX mapping rules
            self.apply_obx_mapping(message)
            
            # Ensure required segments exist for ADT messages
            self.ensure_required_segments(message)
            
            return message
            
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to apply enhancements: {e}",
                "PROCESSING_ERROR"
            )
    
    def apply_obx_mapping(self, message: hl7.Message):
        """Apply OBX segment mapping rules"""
        try:
            obx_segments = []
            segments_to_remove = []
            
            # Collect all OBX segments
            for i, segment in enumerate(message):
                segment_name = str(segment[0][0]) if len(segment) > 0 and len(segment[0]) > 0 else ''
                if segment_name == 'OBX':
                    obx_segments.append((i, segment))
            
            # Apply mapping rules
            for rule in self.mapping_rules:
                for seg_index, obx_segment in obx_segments:
                    # Check if this OBX matches the rule
                    # Extract OBX-3.1 (first component of observation identifier)
                    obx_field_value = ''
                    if len(obx_segment) > 3:
                        obx3_components = str(obx_segment[3]).split('^')
                        obx_field_value = obx3_components[0] if len(obx3_components) > 0 else ''
                    
                    if 'obxValue' in rule and obx_field_value == rule['obxValue']:
                        # Get the value to map (OBX-5)
                        value_to_map = str(obx_segment[5]) if len(obx_segment) > 5 else ''
                        
                        # Always mark for removal if specified, regardless of value
                        if rule.get('removeOriginal', False):
                            segments_to_remove.append(seg_index)
                        
                        # Only proceed with mapping if value is not empty
                        if value_to_map and value_to_map.strip():
                            # Use the trimmed value
                            value_to_map = value_to_map.strip()
                            # Find or create target segment
                            try:
                                target_seg = message.segment(rule['targetSegment'])
                            except KeyError:
                                target_seg = None
                            
                            # If segment doesn't exist, create it
                            if not target_seg:
                                if rule['targetSegment'] == 'ROL':
                                    # Create ROL segment for family physician using message separators
                                    new_rol = hl7.Segment('ROL', ['', '', '', '', ''], separator=message.separator)
                                    # Find position after PID segment
                                    pid_index = -1
                                    for idx, seg in enumerate(message):
                                        if str(seg[0][0]) == 'PID':
                                            pid_index = idx
                                            break
                                    if pid_index >= 0:
                                        message.insert(pid_index + 1, new_rol)
                                        target_seg = new_rol
                                elif rule['targetSegment'] == 'PD1':
                                    # Create PD1 segment for primary facility using message separators
                                    new_pd1 = hl7.Segment('PD1', ['', '', '', ''], separator=message.separator)
                                    # Find position after PID segment
                                    pid_index = -1
                                    for idx, seg in enumerate(message):
                                        if str(seg[0][0]) == 'PID':
                                            pid_index = idx
                                            break
                                    if pid_index >= 0:
                                        message.insert(pid_index + 1, new_pd1)
                                        target_seg = new_pd1
                                else:
                                    self.logger.warning(f"Target segment {rule['targetSegment']} not found and cannot be created for {rule['obxValue']}")
                                    continue
                            
                            if target_seg:
                                # Parse target field (e.g., "3.8")
                                field_parts = rule['targetField'].split('.')
                                field_num = int(field_parts[0])
                                
                                # Ensure the segment has enough fields
                                while len(target_seg) <= field_num:
                                    target_seg.append('')
                                
                                # Set the value
                                if len(field_parts) > 1:
                                    component_num = int(field_parts[1]) - 1
                                    
                                    # Handle component-based field (e.g., PID-3.8)
                                    current_field = target_seg[field_num]
                                    
                                    # Convert field to component structure
                                    if isinstance(current_field, str):
                                        # Split existing field by component separator (^)
                                        if current_field:
                                            components = current_field.split('^')
                                        else:
                                            components = []
                                    elif hasattr(current_field, '__iter__') and not isinstance(current_field, str):
                                        # Field is already a list/container - extract string components
                                        components = []
                                        for item in current_field:
                                            if isinstance(item, str):
                                                components.append(item)
                                            else:
                                                components.append(str(item) if item else '')
                                    else:
                                        components = [str(current_field)] if current_field else []
                                    
                                    # Ensure enough components exist
                                    while len(components) <= component_num:
                                        components.append('')
                                    
                                    # Set the component value
                                    components[component_num] = value_to_map
                                    
                                    # Create the field as a component-separated string
                                    # python-hl7 expects fields to be strings with ^ separators for components
                                    target_seg[field_num] = '^'.join(components)
                                    
                                    self.logger.debug(f"Mapped {rule['obxValue']} = '{value_to_map}' to {rule['targetSegment']}-{rule['targetField']} (component {component_num + 1}), field now: '{target_seg[field_num]}'")
                                else:
                                    # Simple field assignment
                                    target_seg[field_num] = value_to_map
                                    self.logger.debug(f"Mapped {rule['obxValue']} = '{value_to_map}' to {rule['targetSegment']}-{rule['targetField']}")
                                
                                # Removal is already handled above
                                    
                            else:
                                self.logger.warning(f"Could not create or find target segment {rule['targetSegment']} for {rule['obxValue']}")
                        else:
                            self.logger.debug(f"Skipping mapping for {rule['obxValue']} - empty or whitespace-only value: '{value_to_map}'")
            
            # Remove OBX segments (in reverse order to maintain indices)
            for seg_index in sorted(set(segments_to_remove), reverse=True):
                del message[seg_index]
                
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to apply OBX mapping: {e}",
                "MAPPING_RULE_ERROR"
            )
    
    def ensure_required_segments(self, message: hl7.Message):
        """Ensure required segments exist for specific message types"""
        try:
            # Get MSH segment to determine message type
            msh = message.segment('MSH')
            if len(msh) > 9:
                message_type = str(msh[9]).split('^')[0] if '^' in str(msh[9]) else str(msh[9])
                self.logger.debug(f"Message type detected: {message_type}")
                
                # For ADT messages, ensure EVN segment exists
                if message_type == 'ADT':
                    evn_exists = False
                    try:
                        evn_segment = message.segment('EVN')
                        evn_exists = True
                        self.logger.debug("EVN segment already exists")
                    except KeyError:
                        self.logger.debug("EVN segment missing, creating new one")
                        
                    if not evn_exists:
                        # EVN segment missing, create it
                        # EVN segment should be placed after MSH
                        event_type = str(msh[9]).split('^')[1] if '^' in str(msh[9]) and len(str(msh[9]).split('^')) > 1 else 'A04'
                        
                        # Create EVN segment with basic required fields
                        # EVN|Event Type Code|Recorded Date/Time|Date/Time Planned Event|Event Reason Code|Operator ID|Event Occurred|
                        from datetime import datetime
                        current_time = datetime.now().strftime('%Y%m%d%H%M%S')
                        
                        # Create EVN segment fields
                        evn_fields = ['EVN', event_type, current_time, '', '', '', '']
                        from hl7.containers import Segment
                        new_evn = Segment('|', evn_fields)
                        
                        # Insert EVN after MSH (index 1)
                        message.insert(1, new_evn)
                        
                        self.logger.info(f"Created missing EVN segment for ADT message with event type {event_type}")
                        
        except Exception as e:
            self.logger.error(f"Failed to ensure required segments: {e}")
            import traceback
            self.logger.error(f"Stack trace: {traceback.format_exc()}")
    
    def save_enhanced_message(self, message: hl7.Message, original_path: Path):
        """Save enhanced message preserving directory structure"""
        try:
            # Calculate relative path from source directory
            relative_path = original_path.relative_to(self.source_dir)
            output_path = self.output_dir / relative_path
            
            # Create parent directories
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert message back to string with proper HL7 formatting
            # The python-hl7 library stores segments as a list, so we need to join them properly
            segments = []
            for segment in message:
                segments.append(str(segment))
            
            # Join segments with carriage return (\r) as per HL7 standard
            enhanced_content = '\r'.join(segments)
            
            # Write to file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(enhanced_content)
            
            self.logger.info(f"Enhanced message saved: {output_path}")
            
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to save enhanced message: {e}",
                "FILE_WRITE_ERROR",
                str(original_path)
            )
    
    def quarantine_file(self, file_path: Path, error: HL7ProcessingError):
        """Move problematic file to quarantine with error details"""
        try:
            # Calculate relative path
            relative_path = file_path.relative_to(self.source_dir)
            quarantine_path = self.quarantine_dir / relative_path
            
            # Create parent directories
            quarantine_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file to quarantine
            import shutil
            shutil.copy2(file_path, quarantine_path)
            
            # Create error details file
            error_file = quarantine_path.with_suffix('.error.json')
            error_details = {
                'original_path': str(file_path),
                'error_code': error.error_code,
                'error_message': error.message,
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }
            
            with open(error_file, 'w') as f:
                json.dump(error_details, f, indent=2)
            
            self.stats['files_quarantined'] += 1
            self.logger.warning(f"File quarantined: {quarantine_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to quarantine file {file_path}: {e}")
    
    def ai_analyze_error(self, error: HL7ProcessingError, file_content: str = None) -> Dict[str, Any]:
        """Use AI to analyze error and suggest fixes"""
        if not self.ai_enabled:
            return {}
        
        try:
            prompt = f"""
            Analyze this HL7 processing error and suggest automated fixes:

            Error Code: {error.error_code}
            Error Message: {error.message}
            File Path: {error.file_path}

            File Content (first 500 chars):
            {file_content[:500] if file_content else 'N/A'}

            Provide a JSON response with:
            1. root_cause: Brief description of the root cause
            2. suggested_actions: List of specific actions to take
            3. can_auto_fix: Boolean indicating if this can be automatically fixed
            4. priority: high/medium/low

            Focus on actionable solutions for HL7 message processing.
            """
            
            response = self.ai_model.generate_content(prompt)
            
            # Try to parse JSON response
            try:
                ai_analysis = json.loads(response.text)
                return ai_analysis
            except json.JSONDecodeError:
                # Fallback to text response
                return {
                    'root_cause': 'AI analysis available',
                    'suggested_actions': [response.text[:200]],
                    'can_auto_fix': False,
                    'priority': 'medium'
                }
                
        except Exception as e:
            self.logger.debug(f"AI analysis failed: {e}")
            return {}
    
    def process_file(self, file_path: Path) -> bool:
        """Process a single HL7 file"""
        try:
            self.logger.info(f"Processing: {file_path}")
            
            # Parse HL7 message
            message = self.parse_hl7_message(file_path)
            
            # Apply enhancements
            enhanced_message = self.apply_hl7_enhancements(message)
            
            # Save enhanced message
            self.save_enhanced_message(enhanced_message, file_path)
            
            self.stats['files_enhanced'] += 1
            return True
            
        except HL7ProcessingError as e:
            self.stats['errors_encountered'] += 1
            
            # Log error
            self.log_error(e, {'stack': traceback.format_exc()})
            self.logger.error(f"Error processing {file_path}: {e.message}")
            
            # AI analysis
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    file_content = f.read()
                ai_analysis = self.ai_analyze_error(e, file_content)
                if ai_analysis:
                    self.logger.info(f"AI Analysis: {ai_analysis.get('root_cause', 'No analysis')}")
            except:
                pass
            
            # Quarantine file for certain error types
            if e.error_code in ['HL7_PARSING_FAILURE', 'PROCESSING_ERROR']:
                self.quarantine_file(file_path, e)
            
            return False
        
        except Exception as e:
            # Unexpected error
            error = HL7ProcessingError(
                f"Unexpected error: {e}",
                "PROCESSING_ERROR",
                str(file_path)
            )
            self.stats['errors_encountered'] += 1
            self.log_error(error, {'stack': traceback.format_exc()})
            self.logger.error(f"Unexpected error processing {file_path}: {e}")
            return False
    
    def run(self):
        """Main processing loop"""
        start_time = time.time()
        
        self.logger.info("🚀 Starting HL7 Message Enhancement Engine")
        
        try:
            # Find all HL7 files
            hl7_files = self.find_hl7_files()
            
            if not hl7_files:
                self.logger.warning(f"No HL7 files found in {self.source_dir}")
                return
            
            self.logger.info(f"Found {len(hl7_files)} HL7 files to process")
            
            # Process each file
            for file_path in hl7_files:
                self.stats['files_processed'] += 1
                self.process_file(file_path)
            
            # Final statistics
            elapsed_time = time.time() - start_time
            
            self.logger.info("\n✅ PROCESSING COMPLETE")
            self.logger.info(f"Files processed successfully: {self.stats['files_processed']}")
            self.logger.info(f"Files enhanced: {self.stats['files_enhanced']}")
            self.logger.info(f"Errors encountered: {self.stats['errors_encountered']}")
            self.logger.info(f"Files quarantined: {self.stats['files_quarantined']}")
            self.logger.info(f"Processing time: {elapsed_time:.2f} seconds")
            
            if self.stats['errors_encountered'] > 0:
                self.logger.info(f"Check error_details.log for detailed error analysis")
            
        except HL7ProcessingError as e:
            self.logger.error(f"Processing failed: {e.message}")
            self.log_error(e)
            sys.exit(1)
        except Exception as e:
            error = HL7ProcessingError(
                f"Unexpected system error: {e}",
                "PROCESSING_ERROR"
            )
            self.logger.error(f"System error: {e}")
            self.log_error(error, {'stack': traceback.format_exc()})
            sys.exit(1)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='HL7 Message Enhancement Engine with AI-powered error recovery'
    )
    parser.add_argument(
        '--source', 
        default='rawhl7messages',
        help='Source directory for raw HL7 files (default: rawhl7messages)'
    )
    parser.add_argument(
        '--output', 
        default='enhancedHl7',
        help='Output directory for enhanced files (default: enhancedHl7)'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Validate source directory
    if not Path(args.source).exists():
        print(f"❌ Source directory not found: {args.source}")
        sys.exit(1)
    
    # Create and run processor
    try:
        processor = HL7Processor(args.source, args.output, args.verbose)
        processor.run()
    except KeyboardInterrupt:
        print("\n⚠️  Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Failed to initialize processor: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()