2025-07-07 10:47:07,214 - INFO - Loaded 5 mapping rules
2025-07-07 10:47:07,225 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 10:47:07,237 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 10:47:07,541 - INFO - Found 1 HL7 files to process
2025-07-07 10:47:07,561 - INFO - Processing: rawhl7messages\sample-hl7.hl7
2025-07-07 10:47:07,655 - WARNING - Target segment  not found and cannot be created for HC EXP DATE
2025-07-07 10:47:07,700 - ERROR - Error processing rawhl7messages\sample-hl7.hl7: Failed to apply enhancements: Failed to apply OBX mapping: Segment.__init__() got multiple values for argument 'separator'
2025-07-07 10:47:07,718 - WARNING - File quarantined: quarantine\sample-hl7.hl7
2025-07-07 10:47:08,869 - INFO - Files processed successfully: 1
2025-07-07 10:47:08,932 - INFO - Files enhanced: 0
2025-07-07 10:47:08,995 - INFO - Errors encountered: 1
2025-07-07 10:47:09,062 - INFO - Files quarantined: 1
2025-07-07 10:47:09,269 - INFO - Processing time: 0.38 seconds
2025-07-07 10:47:09,372 - INFO - Check error_details.log for detailed error analysis
2025-07-07 10:50:04,779 - INFO - Loaded 5 mapping rules
2025-07-07 10:50:04,780 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 10:50:04,781 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 10:50:04,782 - INFO - Starting HL7 Message Enhancement Engine
2025-07-07 10:50:04,782 - INFO - Found 1 HL7 files to process
2025-07-07 10:50:04,783 - INFO - Processing: rawhl7messages\sample-hl7.hl7
2025-07-07 10:50:04,787 - WARNING - Target segment  not found and cannot be created for HC EXP DATE
2025-07-07 10:50:05,158 - ERROR - Error processing rawhl7messages\sample-hl7.hl7: Failed to apply enhancements: Failed to apply OBX mapping: 
2025-07-07 10:50:05,239 - WARNING - File quarantined: quarantine\sample-hl7.hl7
2025-07-07 10:50:05,268 - INFO - 
PROCESSING COMPLETE
2025-07-07 10:50:05,273 - INFO - Files processed successfully: 1
2025-07-07 10:50:05,274 - INFO - Files enhanced: 0
2025-07-07 10:50:05,276 - INFO - Errors encountered: 1
2025-07-07 10:50:05,276 - INFO - Files quarantined: 1
2025-07-07 10:50:05,277 - INFO - Processing time: 0.49 seconds
2025-07-07 10:50:05,278 - INFO - Check error_details.log for detailed error analysis
2025-07-07 10:50:49,215 - INFO - Loaded 5 mapping rules
2025-07-07 10:50:49,218 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 10:50:49,220 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 10:50:49,222 - INFO - Starting HL7 Message Enhancement Engine
2025-07-07 10:50:49,540 - INFO - Found 1 HL7 files to process
2025-07-07 10:50:49,825 - INFO - Processing: rawhl7messages\sample-hl7.hl7
2025-07-07 10:50:49,908 - WARNING - Target segment  not found and cannot be created for HC EXP DATE
2025-07-07 10:50:50,157 - ERROR - Error processing rawhl7messages\sample-hl7.hl7: Failed to apply enhancements: Failed to apply OBX mapping: 
2025-07-07 10:50:50,333 - WARNING - File quarantined: quarantine\sample-hl7.hl7
2025-07-07 10:50:50,341 - INFO - 
PROCESSING COMPLETE
2025-07-07 10:50:50,344 - INFO - Files processed successfully: 1
2025-07-07 10:50:50,373 - INFO - Files enhanced: 0
2025-07-07 10:50:50,515 - INFO - Errors encountered: 1
2025-07-07 10:50:50,664 - INFO - Files quarantined: 1
2025-07-07 10:50:50,672 - INFO - Processing time: 1.12 seconds
2025-07-07 10:50:50,683 - INFO - Check error_details.log for detailed error analysis
2025-07-07 10:51:32,860 - INFO - Loaded 5 mapping rules
2025-07-07 10:51:32,861 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 10:51:32,862 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 10:51:32,863 - INFO - Starting HL7 Message Enhancement Engine
2025-07-07 10:51:32,864 - INFO - Found 1 HL7 files to process
2025-07-07 10:51:32,864 - INFO - Processing: rawhl7messages\sample-hl7.hl7
2025-07-07 10:51:32,868 - ERROR - Error processing rawhl7messages\sample-hl7.hl7: Failed to apply enhancements: Failed to apply OBX mapping: invalid literal for int() with base 10: 'x'
2025-07-07 10:51:32,872 - WARNING - File quarantined: quarantine\sample-hl7.hl7
2025-07-07 10:51:32,879 - INFO - 
PROCESSING COMPLETE
2025-07-07 10:51:32,884 - INFO - Files processed successfully: 1
2025-07-07 10:51:32,884 - INFO - Files enhanced: 0
2025-07-07 10:51:32,885 - INFO - Errors encountered: 1
2025-07-07 10:51:32,886 - INFO - Files quarantined: 1
2025-07-07 10:51:32,887 - INFO - Processing time: 0.02 seconds
2025-07-07 10:51:32,889 - INFO - Check error_details.log for detailed error analysis
2025-07-07 10:55:34,777 - INFO - Loaded 5 mapping rules
2025-07-07 10:55:34,777 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 10:55:34,779 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 10:55:34,780 - INFO - Starting HL7 Message Enhancement Engine
2025-07-07 10:55:34,781 - INFO - Found 1 HL7 files to process
2025-07-07 10:55:34,781 - INFO - Processing: rawhl7messages\sample-hl7.hl7
2025-07-07 10:55:34,784 - DEBUG - Mapped QATAR_ID_EXP = '20240212' to PID-3.x.8 (component 8), field now: 'HC02287391^^^MRN^MR^RHAPSODY_CON_SYS^29388600403^^^"MOI"^"SSN"^^^20240212^HC02287391^^^MRN^MRN^^625664-I^03050476^^0^0^^^^^PHCC Historical MRN^Passport^^Historical MRN^Passport^^medicom^^^^^^20240212'
2025-07-07 10:55:34,786 - WARNING - Target segment  not found and cannot be created for HC EXP DATE
2025-07-07 10:55:34,787 - DEBUG - Mapped FAMILY_PHYSICIAN = '10096519^Wally^Ahmed^Nourelfalah Mahmoud' to ROL-4
2025-07-07 10:55:34,789 - DEBUG - Mapped PRIM_ORG_NAME = 'Rawdat Al Khail Health Center' to PD1-3
2025-07-07 10:55:34,796 - WARNING - Target segment  not found and cannot be created for FULLREG
2025-07-07 10:55:34,798 - DEBUG - Message type detected: ADT
2025-07-07 10:55:34,799 - DEBUG - EVN segment already exists
2025-07-07 10:55:34,802 - INFO - Enhanced message saved: sample-hl7.hl7
2025-07-07 10:55:34,814 - INFO - 
PROCESSING COMPLETE
2025-07-07 10:55:35,042 - INFO - Files processed successfully: 1
2025-07-07 10:55:35,054 - INFO - Files enhanced: 1
2025-07-07 10:55:35,055 - INFO - Errors encountered: 0
2025-07-07 10:55:35,056 - INFO - Files quarantined: 0
2025-07-07 10:55:35,057 - INFO - Processing time: 0.03 seconds
2025-07-07 10:56:27,960 - INFO - Loaded 5 mapping rules
2025-07-07 10:56:27,961 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 10:56:27,962 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 10:56:27,964 - DEBUG - Mapped FAMILY_PHYSICIAN = '10096519^Wally^Ahmed^Nourelfalah Mahmoud' to ROL-4
2025-07-07 10:56:27,965 - DEBUG - Message type detected: ADT
2025-07-07 10:56:27,966 - DEBUG - EVN segment already exists
2025-07-07 10:57:13,335 - INFO - Loaded 5 mapping rules
2025-07-07 10:57:13,335 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 10:57:13,337 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 10:57:13,339 - WARNING - Target segment  not found and cannot be created for HC EXP DATE
2025-07-07 10:57:13,340 - DEBUG - Mapped FAMILY_PHYSICIAN = 'DR. SMITH^JOHN^A' to ROL-4
2025-07-07 10:57:13,343 - DEBUG - Mapped PRIM_ORG_NAME = 'GENERAL HOSPITAL' to PD1-3
2025-07-07 10:57:13,344 - WARNING - Target segment  not found and cannot be created for FULLREG
2025-07-07 10:57:13,345 - DEBUG - Message type detected: ADT
2025-07-07 10:57:13,345 - DEBUG - EVN segment already exists
2025-07-07 10:59:16,016 - INFO - Loaded 5 mapping rules
2025-07-07 10:59:16,034 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 10:59:16,040 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 10:59:16,043 - WARNING - Target segment  not found and cannot be created for HC EXP DATE
2025-07-07 10:59:16,048 - DEBUG - Mapped FAMILY_PHYSICIAN = 'DR. SMITH^JOHN^A' to ROL-4
2025-07-07 10:59:16,049 - DEBUG - Mapped PRIM_ORG_NAME = 'GENERAL HOSPITAL' to PD1-3
2025-07-07 10:59:16,050 - WARNING - Target segment  not found and cannot be created for FULLREG
2025-07-07 10:59:16,050 - DEBUG - Message type detected: ADT
2025-07-07 10:59:16,051 - DEBUG - EVN segment already exists
2025-07-07 11:00:29,660 - INFO - Loaded 5 mapping rules
2025-07-07 11:00:29,663 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 11:00:29,664 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 11:00:29,667 - WARNING - Target segment  not found and cannot be created for HC EXP DATE
2025-07-07 11:00:29,667 - DEBUG - Mapped FAMILY_PHYSICIAN = 'DR. SMITH^JOHN^A' to ROL-4
2025-07-07 11:00:29,668 - DEBUG - Mapped PRIM_ORG_NAME = 'GENERAL HOSPITAL' to PD1-3
2025-07-07 11:00:29,668 - WARNING - Target segment  not found and cannot be created for FULLREG
2025-07-07 11:00:29,996 - DEBUG - Message type detected: ADT
2025-07-07 11:00:30,101 - DEBUG - EVN segment already exists
2025-07-07 11:01:16,793 - INFO - Loaded 5 mapping rules
2025-07-07 11:01:16,796 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 11:01:16,798 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 11:01:16,805 - WARNING - Target segment  not found and cannot be created for HC EXP DATE
2025-07-07 11:01:16,815 - DEBUG - Mapped FAMILY_PHYSICIAN = 'DR. SMITH^JOHN^A' to ROL-4
2025-07-07 11:01:16,843 - DEBUG - Mapped PRIM_ORG_NAME = 'GENERAL HOSPITAL' to PD1-3
2025-07-07 11:01:16,844 - WARNING - Target segment  not found and cannot be created for FULLREG
2025-07-07 11:01:16,844 - DEBUG - Message type detected: ADT
2025-07-07 11:01:16,845 - DEBUG - EVN segment already exists
2025-07-07 11:01:46,987 - INFO - Loaded 5 mapping rules
2025-07-07 11:01:46,989 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 11:01:46,991 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 11:01:46,993 - DEBUG - Target segment  not found
2025-07-07 11:01:47,148 - WARNING - Target segment  not found and cannot be created for HC EXP DATE
2025-07-07 11:01:47,156 - DEBUG - Target segment ROL not found
2025-07-07 11:01:47,157 - DEBUG - Mapped FAMILY_PHYSICIAN = 'DR. SMITH^JOHN^A' to ROL-4
2025-07-07 11:01:47,159 - DEBUG - Found existing PD1 segment: PD1|||||||||||||||||||||||||||||||
2025-07-07 11:01:47,159 - DEBUG - Mapped PRIM_ORG_NAME = 'GENERAL HOSPITAL' to PD1-3
2025-07-07 11:01:47,251 - DEBUG - Target segment  not found
2025-07-07 11:01:47,252 - WARNING - Target segment  not found and cannot be created for FULLREG
2025-07-07 11:01:47,255 - DEBUG - Message type detected: ADT
2025-07-07 11:01:47,317 - DEBUG - EVN segment already exists
2025-07-07 11:02:50,567 - INFO - Loaded 5 mapping rules
2025-07-07 11:02:50,741 - WARNING - GEMINI_API_KEY not found, AI recovery disabled
2025-07-07 11:02:50,817 - WARNING - Failed to clear output directory: [WinError 5] Access is denied: '.\\.git\\hooks'
2025-07-07 11:02:50,839 - DEBUG - Target segment  not found
2025-07-07 11:02:50,847 - WARNING - Target segment  not found and cannot be created for HC EXP DATE
2025-07-07 11:02:50,920 - DEBUG - Target segment ROL not found
2025-07-07 11:02:50,940 - DEBUG - Mapped FAMILY_PHYSICIAN = 'DR. SMITH^JOHN^A' to ROL-4
2025-07-07 11:02:50,947 - DEBUG - Found existing PD1 segment: PD1|||||||||||||||||||||||||||||||
2025-07-07 11:02:50,997 - DEBUG - Mapped PRIM_ORG_NAME = 'GENERAL HOSPITAL' to PD1-3
2025-07-07 11:02:51,066 - DEBUG - Target segment  not found
2025-07-07 11:02:51,091 - WARNING - Target segment  not found and cannot be created for FULLREG
2025-07-07 11:02:51,093 - DEBUG - Removed OBX segment: OBX|1|ST|HC EXP DATE^Health Card Expiration Date||20251231||||||F
2025-07-07 11:02:51,094 - DEBUG - Removed OBX segment: OBX|4|ST|FAMILY_PHYSICIAN^Family Physician||DR. SMITH^JOHN^A||||||F
2025-07-07 11:02:51,094 - DEBUG - Removed OBX segment: OBX|3|ST|PRIM_ORG_NAME^Primary Organization Name||GENERAL HOSPITAL||||||F
2025-07-07 11:02:51,095 - DEBUG - Removed OBX segment: OBX|2|ST|FULLREG^Full Registration||Y||||||F
2025-07-07 11:02:51,096 - DEBUG - Message type detected: ADT
2025-07-07 11:02:51,096 - DEBUG - EVN segment already exists
